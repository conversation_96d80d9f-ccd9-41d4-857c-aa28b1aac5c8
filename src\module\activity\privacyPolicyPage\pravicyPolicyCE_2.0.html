<!DOCTYPE html>
<html lang="zh-CN">
<head>
<meta charset="UTF-8" />
<meta name="viewport" content="width=device-width, initial-scale=1" />
<title>【瑞影云++】隐私政策</title>
<style>
.privacy-container{
    background-color: #fff;
    min-height: 100vh;
    /* 全局设置：让整个隐私政策容器内的所有文本都可以被选择 */
    user-select: text !important;
    -webkit-user-select: text !important;
    -moz-user-select: text !important;
    -ms-user-select: text !important;
}
#privacy-agreement {
    font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', 'PingFang SC', 'Hiragino Sans GB', 'Microsoft YaHei', 'Helvetica Neue', Helvetica, Arial, sans-serif;
    padding: 24px;
    max-width: 1200px;
    margin: 0 auto;
    background: #fff;
    color: #333;
    line-height: 1.7;
    font-size: 16px;
}
.header {
    text-align: center;
    margin-bottom: 32px;
    padding-bottom: 24px;
    border-bottom: 1px solid #e8e8e8;
}
.header h1 {
    font-size: 28px;
    font-weight: 600;
    color: #1a1a1a;
    margin-bottom: 16px;
    line-height: 1.3;
}
.header p {
    color: #666;
    font-size: 14px;
    margin: 0;
}
.section {
    margin-bottom: 32px;
    border: 1px solid #e8e8e8;
    padding: 24px;
    border-radius: 8px;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.06);
    background: #fff;
    transition: box-shadow 0.3s ease;
}
.section:hover {
    box-shadow: 0 4px 16px rgba(0, 0, 0, 0.1);
}
.section h2 {
    font-size: 20px;
    font-weight: 600;
    color: #1a1a1a;
    border-bottom: 2px solid #4a90e2;
    padding-bottom: 8px;
    margin-bottom: 16px;
    line-height: 1.4;
}
.content {
    line-height: 1.7;
}
.content p {
    margin-bottom: 16px;
    color: #333;
}
.content p:last-child {
    margin-bottom: 0;
}
.content h3, .content h4 {
    font-weight: 600;
    color: #1a1a1a;
    margin: 20px 0 12px 0;
    line-height: 1.4;
}
.content h3 {
    font-size: 18px;
}
.content h4 {
    font-size: 16px;
}
.content ul, .content ol {
    padding-left: 24px;
    margin: 16px 0;
}
.content ul li {
    list-style: disc;
    margin-bottom: 8px;
    line-height: 1.6;
    color: #333;
}
.content ul li strong {
    color: #1a1a1a;
    font-weight: 600;
}
.content strong {
    color: #1a1a1a;
    font-weight: 600;
}
table {
    width: 100%;
    border-collapse: collapse;
    margin: 20px 0;
    border-radius: 8px;
    overflow: hidden;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.06);
    table-layout: auto; /* 让表格自动调整列宽 */
}
th {
    background: linear-gradient(135deg, #4a90e2 0%, #357abd 100%);
    color: #fff;
    font-weight: 600;
    padding: 16px 12px;
    text-align: left;
    font-size: 14px;
    border: none;
    word-wrap: break-word;
    white-space: normal;
    vertical-align: top;
}
td {
    padding: 16px 12px;
    border-bottom: 1px solid #f0f0f0;
    border-right: 1px solid #f0f0f0;
    color: #333;
    font-size: 14px;
    line-height: 1.6;
    word-wrap: break-word;
    word-break: break-all;
    white-space: normal;
    vertical-align: top;
}
td:last-child {
    border-right: none;
}
td a {
    color: #4a90e2;
    text-decoration: none;
    word-break: break-all;
}
td a:hover {
    text-decoration: underline;
}
tr:last-child td {
    border-bottom: none;
}
tr:nth-child(even) {
    background-color: #fafafa;
}
tr:hover {
    background-color: #f5f5f5;
}
.permission-table,
.sdk-table {
    th, td {
        text-align: center;
    }
}
/* 响应式设计 */
@media (max-width: 768px) {
    #privacy-agreement {
        padding: 16px;
        font-size: 15px;
    }
    .header {
        margin-bottom: 24px;
        padding-bottom: 16px;
    }
    .header h1 {
        font-size: 24px;
        margin-bottom: 12px;
    }
    .header p {
        font-size: 13px;
    }
    .section {
        margin-bottom: 24px;
        padding: 16px;
        border-radius: 6px;
    }
    .section h2 {
        font-size: 18px;
        margin-bottom: 12px;
        padding-bottom: 6px;
    }
    .content h3 {
        font-size: 16px;
        margin: 16px 0 8px 0;
    }
    .content h4 {
        font-size: 15px;
        margin: 12px 0 6px 0;
    }
    .content p {
        margin-bottom: 12px;
    }
    .content ul, .content ol {
        padding-left: 20px;
        margin: 12px 0;
    }
    .content ul li {
        margin-bottom: 6px;
    }
    table {
        font-size: 13px;
        margin: 16px 0;
    }
    th, td {
        padding: 12px 8px;
        word-wrap: break-word;
        word-break: break-all;
        white-space: normal;
    }
}
@media (max-width: 480px) {
    #privacy-agreement {
        padding: 12px;
        font-size: 14px;
    }
    .header h1 {
        font-size: 22px;
    }
    .section {
        padding: 12px;
    }
    .section h2 {
        font-size: 16px;
    }
    table {
        font-size: 12px;
    }
    th, td {
        padding: 8px 6px;
    }
}
</style>
</head>
<body>
<div class="privacy-container">
<div id="privacy-agreement">
    <div class="header">
        <h1>【Mico+】Privacy Policy</h1>
        <p>Last updated: [30th July] 2025</p>
    </div>

    <div class="section">
        <div class="content">
            <p><strong>Shenzhen Mindray Bio-Medical Electronics Co., Ltd.</strong> ("We" or "Mindray", a company organised under the P.R.C. law with its registered office at Mindray Building, Keji 12th Road South, High-tech Industrial Park, Shenzhen 518057, P.R.C.) respects your right to privacy and values the protection of your Personal Data. This [Mico+] Privacy Policy ("Privacy Policy") explains how we collect, process and protect your Personal Data when you use our [Mico+ Platform] ("Platform") and interact with us by other means, and how you can exercise your rights to your Personal Data.</p>

            <p><strong>Please note that our Platform and the relevant services are intended to be used solely by healthcare professionals and healthcare organisations (e.g. hospitals, clinics). If you are not healthcare professionals or not from healthcare organisations, please do not use our Platform or relevant services, or submit your Personal Data to us.</strong></p>

            <p><strong>Please note that this Privacy Policy does not apply to other third-party products or services that you access through our products or services, as the detailed information is specified in such third party's privacy policy or similar statement. We recommend that you read and confirm that you understand such privacy policy before accepting the relevant services and providing Personal Data.</strong></p>

            <p><strong>This Privacy Policy informs you of the following information:</strong></p>
            <ol>
                <li>Who are the data controllers of your Personal Data?</li>
                <li>Types of Personal Data we collect and purpose</li>
                <li>Notice for cookies and related technologies</li>
                <li>How do we process Personal Data of minors under the age of 18</li>
                <li>How do we share and entrust the processing of your Personal Data</li>
                <li>How do we protect your Personal Data</li>
                <li>How long do we store your Personal Data</li>
                <li>International transfer of your Personal Data</li>
                <li>Your rights</li>
                <li>How do we update this Privacy Policy</li>
                <li>How to contact us</li>
            </ol>
        </div>
    </div>

    <div class="section">
        <h2>1. Who are the data controllers of your personal data?</h2>
        <div class="content">
            <p>With regard to the <strong>user data of healthcare professionals and healthcare organizations</strong> related to account creation, management, and usage of the Platform, <strong>Mindray</strong> is the Data Controller responsible for such Personal Data.</p>

            <p>For <strong>patients' Personal Data</strong> that may be collected and processed through our Platform by healthcare professionals and healthcare organizations, the healthcare professional or the organization providing healthcare service to the patient is the Data Controller. We act solely as a Data Processor and process such Personal Data following their instructions. If you are a patient of a healthcare professional or organization using our Platform and have questions or concerns about the processing of your Personal Data, you should contact the relevant healthcare professional or organization directly or review their privacy notices, where available.</p>

            <p><strong>As a healthcare professional, you are responsible for ensuring that any processing of patient Personal Data, including, but not limited to, its collection, uploading to the Platform, and any sharing with others through the Platform, is carried out in full compliance with all applicable laws and regulations, including those relating to data protection and patient confidentiality. It is your obligation to inform patients about the processing of their Personal Data, obtain all necessary authorizations or consents, and ensure that any use of the Platform aligns with your legal and professional responsibilities.</strong></p>
        </div>
    </div>

    <div class="section">
        <h2>2. Types of Personal Data we collect and purpose</h2>
        <div class="content">
            <h3>(1) Personal Data you voluntarily provide to us</h3>

            <p><strong>When you register for or log in to our Platform,</strong> we will collect your mobile number or email to verify the uniqueness of your identity and complete your account registration. If you do not provide the above information, you may not be able to use the Platform services normally. We will also collect your occupational identity and your institution to verify whether you are healthcare professionals or from healthcare organizations. After registration, the Platform will automatically generate account information (typically including your nickname, profile photo, and account name). You can choose to provide or edit institution information, personal profile, gender, nickname, profile photo according to your own needs. If you do not provide such information, it will not affect your use of the Platform.</p>

            <p><strong>When you use chat-related functions on our Platform,</strong> including friend chat, group chat, file transfer assistant, etc., and when you perform text input, voice input, image or file upload, etc., the Platform will store your chat history in the cloud to ensure they are not lost. After you withdraw the chat history on the Platform, it will be deleted simultaneously on the Platform cloud; if you do not actively withdraw the chat history, you can still view the chat history on the Platform cloud after reinstalling the Platform on your device or logging in with a new device.</p>

            <p><strong>When you use the "Search Discussion Records" feature,</strong> the platform cloud will store your search keywords, as this data is required for the functionality to operate.</p>

            <p><strong>When you use the real time consultation function on our Platform,</strong> you can choose whether to enable the cloud recording function. If you enable the cloud recording function, the Platform's cloud will store the recorded content of the live voice and video. And the ultrasound image in the real-time video may include, among others, patient names, patient IDs, other IDs, gender, date of birth, age, diagnostic information, etc. If you do not want the above-mentioned ultrasound images with patient information to be recorded, please make sure to hide the patient information on the ultrasound main interface on the ultrasound device or directly not enable cloud recording; if you start cloud recording and select the relevant function to not hide patient information, the relevant patient personal information may be retained in the platform along with the image.</p>

            <p><strong>When you use related functions on the platform that involve uploading or forwarding pictures,</strong> such as friend chat, group chat, file transfer assistant, personal collection, group collection, etc., because you may upload or forward ultrasound pictures, and the ultrasound pictures may contain patient information including patient names, ID, gender, date of birth, age, diagnosis information, etc. If you do not want the above ultrasound pictures to have patient information, please make sure to hide the relevant patient information when you upload or forward the ultrasound pictures to the Platform. If you choose to not hide, the relevant patient information will be stored in the cloud of the platform; but if you want to delete it, you can delete the relevant ultrasound pictures at any time, and the cloud of the platform will also directly delete the relevant information synchronously.</p>

            <p><strong>When you use the add friend function on our Platform,</strong> your nickname, profile photo, and gender will be displayed to the contacts who you add. Displaying this information allows the contacts to consider whether they agree to be added as a friend by you, and you can update the above information at any time. After successfully adding a friend, your personal collection will be displayed to him/her according to if you want, and you can also delete personal collection-related information at any time.</p>

            <p><strong>When you use the group chat function on our Platform,</strong> the group chat name, group description, group collections, all files, live video reservation information, BI and other information will be stored in the cloud of the Platform so that you can also access this information when you log in to this Platform on other devices. Your profile photo, nickname and gender will be displayed to group members, and you can update the above information at any time.</p>

            <p><strong>When you use the collection function on our Platform to store various types of information,</strong> the information will be uploaded and stored on the cloud of the Platform so that you can also access this information when you log in to the Platform on other devices. Your collection will be visible to your friends based on whether you choose to disclose it. You can also delete your collection information at any time.</p>

            <p><strong>When you upload, like and comment on relevant pictures, videos and other files on the platform,</strong> the information (including the information you choose to upload, for instance, any patient data like ultrasound pictures, and examination records) will be uploaded and stored in the cloud of the Platform so that you can also access this information when you log in to the Platform on other devices. Your likes will not be displayed publicly, while public comments are in public mode. You can also cancel your likes or delete your comment information at any time.</p>

            <p><strong>When you use our Platform to communicate with other software or hardware,</strong> the providers of other software or hardware can obtain relevant information that you actively provide, disclose or transmit on the Platform with your consent. You should fully understand the product functions and Personal Data processing rules of other software or hardware before making your choice. You are responsible for ensuring that you will not violate any individual's Personal Data, especially patient privacy, before you disclose their Personal Data with other software or hardware.</p>
        </div>
    </div>
</div>
</div>
<script>
function handleLinkClick(event, url) {
    event.preventDefault(); // 阻止默认的链接跳转行为

    // 检测是否在iframe中
    const isInIframe = window.self !== window.top;

    if (isInIframe) {
        // 如果在iframe中，向父页面发送消息
        window.parent.postMessage({
            type: 'PRIVACY_LINK_CLICK',
            url: url
        }, '*');
        console.log('在iframe中，已发送消息给父页面:', url);
    } else {
        // 如果不在iframe中，直接打开新页面
        window.open(url, '_blank');
        console.log('不在iframe中，直接打开新页面:', url);
    }
}
</script>
</body>
</html>
